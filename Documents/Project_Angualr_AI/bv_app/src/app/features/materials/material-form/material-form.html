<div class="material-form-container">
  <!-- Header -->
  <div class="page-header">
    <h1>{{ getTitle() }}</h1>
    <button mat-icon-button (click)="onCancel()" title="Back to Materials">
      <mat-icon>arrow_back</mat-icon>
    </button>
  </div>

  @if (isLoading()) {
    <div class="loading-container">
      <mat-spinner></mat-spinner>
      <p>Loading form data...</p>
    </div>
  } @else {
    <!-- Material Form -->
    <mat-card class="form-card">
      <mat-card-content>
        <form [formGroup]="materialForm" (ngSubmit)="onSubmit()" class="material-form">
          <div class="form-row">
            <!-- Material Name -->
            <mat-form-field appearance="outline" class="full-width">
              <mat-label>Material Name</mat-label>
              <input matInput formControlName="name" required>
              @if (materialForm.get('name')?.hasError('required') && materialForm.get('name')?.touched) {
                <mat-error>Material name is required</mat-error>
              }
              @if (materialForm.get('name')?.hasError('maxlength')) {
                <mat-error>Material name cannot exceed 30 characters</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Sales Type -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Sales Type</mat-label>
              <mat-select formControlName="sales_type" required>
                <mat-option value="single">Single</mat-option>
                <mat-option value="group">Group</mat-option>
              </mat-select>
              @if (materialForm.get('sales_type')?.hasError('required') && materialForm.get('sales_type')?.touched) {
                <mat-error>Sales type is required</mat-error>
              }
            </mat-form-field>

            <!-- Design -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Design</mat-label>
              <mat-select formControlName="design_id" required>
                @for (design of designs(); track design.id) {
                  <mat-option [value]="design.id">
                    {{ design.name }}{{ design.color && design.color !== 'NA' ? ' - ' + design.color : '' }}
                  </mat-option>
                }
              </mat-select>
              @if (materialForm.get('design_id')?.hasError('required') && materialForm.get('design_id')?.touched) {
                <mat-error>Design is required</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Material Type -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Material Type</mat-label>
              <mat-select formControlName="type_id" required>
                @for (type of materialTypes(); track type.id) {
                  <mat-option [value]="type.id">{{ type.name }}</mat-option>
                }
              </mat-select>
              @if (materialForm.get('type_id')?.hasError('required') && materialForm.get('type_id')?.touched) {
                <mat-error>Material type is required</mat-error>
              }
            </mat-form-field>

            <!-- Unit -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Unit</mat-label>
              <mat-select formControlName="unit_id" required>
                @for (unit of units(); track unit.id) {
                  <mat-option [value]="unit.id">{{ unit.name }}</mat-option>
                }
              </mat-select>
              @if (materialForm.get('unit_id')?.hasError('required') && materialForm.get('unit_id')?.touched) {
                <mat-error>Unit is required</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Unit Order Price -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Unit Order Price</mat-label>
              <input matInput type="number" formControlName="unit_order_price" required min="0.01" step="0.01">
              <span matPrefix>₹&nbsp;</span>
              @if (materialForm.get('unit_order_price')?.hasError('required') && materialForm.get('unit_order_price')?.touched) {
                <mat-error>Unit order price is required</mat-error>
              }
              @if (materialForm.get('unit_order_price')?.hasError('min')) {
                <mat-error>Unit order price must be greater than 0</mat-error>
              }
            </mat-form-field>

            <!-- Opening Stock Quantity -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Opening Stock Quantity</mat-label>
              <input matInput type="number" formControlName="opening_stock_quantity" required min="0" step="0.01">
              @if (materialForm.get('opening_stock_quantity')?.hasError('required') && materialForm.get('opening_stock_quantity')?.touched) {
                <mat-error>Opening stock quantity is required</mat-error>
              }
              @if (materialForm.get('opening_stock_quantity')?.hasError('min')) {
                <mat-error>Opening stock quantity cannot be negative</mat-error>
              }
            </mat-form-field>
          </div>

          <div class="form-row">
            <!-- Opening Stock Unit Price -->
            <mat-form-field appearance="outline" class="half-width">
              <mat-label>Opening Stock Unit Price</mat-label>
              <input matInput type="number" formControlName="opening_stock_unit_price" required min="0" step="0.01">
              <span matPrefix>₹&nbsp;</span>
              @if (materialForm.get('opening_stock_unit_price')?.hasError('required') && materialForm.get('opening_stock_unit_price')?.touched) {
                <mat-error>Opening stock unit price is required</mat-error>
              }
              @if (materialForm.get('opening_stock_unit_price')?.hasError('min')) {
                <mat-error>Opening stock unit price cannot be negative</mat-error>
              }
            </mat-form-field>
          </div>

          <!-- Form Actions -->
          <div class="form-actions">
            <button mat-button type="button" (click)="onCancel()">
              Cancel
            </button>
            <button mat-raised-button
                    color="primary"
                    type="submit"
                    [disabled]="materialForm.invalid || isSaving()">
              @if (isSaving()) {
                <mat-spinner diameter="20"></mat-spinner>
                <span>{{ isEditMode ? 'Updating...' : 'Creating...' }}</span>
              } @else {
                <span>{{ isEditMode ? 'Update Material' : 'Create Material' }}</span>
              }
            </button>
          </div>
        </form>
      </mat-card-content>
    </mat-card>
  }
</div>
