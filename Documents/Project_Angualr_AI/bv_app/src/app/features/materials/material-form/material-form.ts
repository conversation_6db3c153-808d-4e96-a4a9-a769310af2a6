import { Component, signal, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MaterialService, Material, Design, Unit, MaterialType, MaterialCreateRequest } from '../../../core/services/material';

@Component({
  selector: 'app-material-form',
  templateUrl: './material-form.html',
  styleUrl: './material-form.scss',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatCardModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ]
})
export class MaterialForm implements OnInit {
  private materialService = inject(MaterialService);
  private fb = inject(FormBuilder);
  private router = inject(Router);
  private route = inject(ActivatedRoute);
  private snackBar = inject(MatSnackBar);

  // Signals for state management
  private loadingSignal = signal<boolean>(false);
  private savingSignal = signal<boolean>(false);
  private designsSignal = signal<Design[]>([]);
  private unitsSignal = signal<Unit[]>([]);
  private materialTypesSignal = signal<MaterialType[]>([]);
  private materialSignal = signal<Material | null>(null);

  // Public readonly signals
  isLoading = this.loadingSignal.asReadonly();
  isSaving = this.savingSignal.asReadonly();
  designs = this.designsSignal.asReadonly();
  units = this.unitsSignal.asReadonly();
  materialTypes = this.materialTypesSignal.asReadonly();
  material = this.materialSignal.asReadonly();

  materialForm: FormGroup = this.fb.group({
    name: ['', [Validators.required, Validators.maxLength(30)]],
    sales_type: ['single', [Validators.required]],
    design_id: ['', [Validators.required]],
    type_id: ['', [Validators.required]],
    unit_id: ['', [Validators.required]],
    unit_order_price: ['', [Validators.required, Validators.min(0.01)]],
    opening_stock_quantity: [0, [Validators.required, Validators.min(0)]],
    opening_stock_unit_price: [0, [Validators.required, Validators.min(0)]]
  });

  materialId: number | null = null;
  isEditMode = false;

  ngOnInit(): void {
    this.loadFormData();
    this.checkEditMode();
  }

  private checkEditMode(): void {
    const id = this.route.snapshot.paramMap.get('id');
    if (id && id !== 'new') {
      this.materialId = parseInt(id, 10);
      this.isEditMode = true;
      this.loadMaterial();
    }
  }

  private loadFormData(): void {
    this.loadingSignal.set(true);

    // Load all required data for the form
    Promise.all([
      this.materialService.getDesigns().toPromise(),
      this.materialService.getUnits().toPromise(),
      this.materialService.getMaterialTypes().toPromise()
    ]).then(([designs, units, materialTypes]) => {
      this.designsSignal.set(designs || []);
      this.unitsSignal.set(units || []);
      this.materialTypesSignal.set(materialTypes || []);
      this.loadingSignal.set(false);
    }).catch(error => {
      console.error('Error loading form data:', error);
      this.loadingSignal.set(false);
      this.snackBar.open('Error loading form data', 'Close', { duration: 3000 });
    });
  }

  private loadMaterial(): void {
    if (!this.materialId) return;

    this.materialService.getMaterial(this.materialId).subscribe({
      next: (material) => {
        this.materialSignal.set(material);
        this.materialForm.patchValue({
          name: material.name,
          sales_type: material.sales_type,
          design_id: material.design.id,
          type_id: material.type.id,
          unit_id: material.unit.id,
          unit_order_price: material.unit_order_price,
          opening_stock_quantity: material.opening_stock_quantity,
          opening_stock_unit_price: material.opening_stock_unit_price
        });
      },
      error: (error) => {
        console.error('Error loading material:', error);
        this.snackBar.open('Error loading material', 'Close', { duration: 3000 });
        this.router.navigate(['/materials']);
      }
    });
  }

  onSubmit(): void {
    if (this.materialForm.valid) {
      this.savingSignal.set(true);
      const formData: MaterialCreateRequest = this.materialForm.value;

      const operation = this.isEditMode && this.materialId
        ? this.materialService.updateMaterial(this.materialId, formData)
        : this.materialService.createMaterial(formData);

      operation.subscribe({
        next: () => {
          this.savingSignal.set(false);
          const message = this.isEditMode ? 'Material updated successfully' : 'Material created successfully';
          this.snackBar.open(message, 'Close', { duration: 3000 });
          this.router.navigate(['/materials']);
        },
        error: (error) => {
          console.error('Error saving material:', error);
          this.savingSignal.set(false);
          this.snackBar.open('Error saving material', 'Close', { duration: 3000 });
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.materialForm.controls).forEach(key => {
      const control = this.materialForm.get(key);
      control?.markAsTouched();
    });
  }

  onCancel(): void {
    this.router.navigate(['/materials']);
  }

  getTitle(): string {
    return this.isEditMode ? 'Edit Material' : 'Add New Material';
  }
}
