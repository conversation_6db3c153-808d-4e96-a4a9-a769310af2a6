import { Component, inject } from '@angular/core';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { AsyncPipe, CommonModule } from '@angular/common';
import { MatToolbarModule } from '@angular/material/toolbar';
import { MatButtonModule } from '@angular/material/button';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatListModule } from '@angular/material/list';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatDividerModule } from '@angular/material/divider';
import { RouterModule } from '@angular/router';
import { Observable } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { Auth } from '../core/auth/auth';

@Component({
  selector: 'app-navbar',
  templateUrl: './navbar.component.html',
  styleUrl: './navbar.component.scss',
  imports: [
    CommonModule,
    MatToolbarModule,
    MatButtonModule,
    MatSidenavModule,
    MatListModule,
    MatIconModule,
    MatMenuModule,
    MatDividerModule,
    RouterModule,
    AsyncPipe,
  ]
})
export class NavbarComponent {
  private breakpointObserver = inject(BreakpointObserver);
  private auth = inject(Auth);

  isHandset$: Observable<boolean> = this.breakpointObserver.observe(Breakpoints.Handset)
    .pipe(
      map(result => result.matches),
      shareReplay()
    );

  // Auth signals
  user = this.auth.user;
  isAuthenticated = this.auth.isAuthenticated;

  menuItems = [
    { icon: 'dashboard', label: 'Dashboard', route: '/dashboard' },
    { icon: 'inventory_2', label: 'Materials', route: '/materials' },
    { icon: 'local_shipping', label: 'Suppliers', route: '/suppliers' },
    { icon: 'people', label: 'Customers', route: '/customers' },
    { icon: 'shopping_cart', label: 'Orders', route: '/orders' },
    { icon: 'receipt', label: 'GST', route: '/gst' },
    { icon: 'assessment', label: 'Reports', route: '/reports' },
  ];

  logout(): void {
    this.auth.logout().subscribe();
  }
}
